import subprocess
import os

def overlay_image_on_video(
    input_video,
    input_image,
    output_video,
    opacity=0.3,
    position="center",
    ffmpeg_path=r"D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe"
):
    """
    将图片以指定透明度叠加到视频上
    
    参数:
    input_video: 输入视频文件路径
    input_image: 输入图片文件路径
    output_video: 输出视频文件路径
    opacity: 透明度，0-1之间，默认0.3
    position: 图片位置，可选"center", "top-left", "top-right", "bottom-left", "bottom-right"
              或自定义坐标如"10:20"表示x=10, y=20
    ffmpeg_path: FFmpeg可执行文件路径
    """
    # 验证FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"FFmpeg未找到，请检查路径: {ffmpeg_path}")
    
    # 验证输入文件是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频不存在: {input_video}")
    
    if not os.path.exists(input_image):
        raise FileNotFoundError(f"输入图片不存在: {input_image}")
    
    # 确定图片位置
    if position == "center":
        overlay_pos = "(W-w)/2:(H-h)/2"
    elif position == "top-left":
        overlay_pos = "10:10"  # 左上角，带10px边距
    elif position == "top-right":
        overlay_pos = "(W-w-10):10"  # 右上角，带10px边距
    elif position == "bottom-left":
        overlay_pos = "10:(H-h-10)"  # 左下角，带10px边距
    elif position == "bottom-right":
        overlay_pos = "(W-w-10):(H-h-10)"  # 右下角，带10px边距
    else:
        overlay_pos = position  # 使用自定义坐标
    
    # 构建FFmpeg命令
    cmd = [
        ffmpeg_path,
        "-i", input_video,
        "-i", input_image,
        "-filter_complex",
        f"[1:v]format=rgba,colorchannelmixer=aa={opacity}[watermark];[0:v][watermark]overlay={overlay_pos}:shortest=0",
        "-c:a", "copy",  # 保持音频不变
        "-y",  # 覆盖现有文件
        output_video
    ]
    
    try:
        # 执行命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 检查是否有错误
        if result.returncode != 0:
            print(f"FFmpeg错误输出: {result.stderr}")
            raise RuntimeError(f"FFmpeg处理失败，返回代码: {result.returncode}")
            
        print(f"处理完成，输出文件: {output_video}")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 示例用法
    input_video_path = "input.mp4"      # 输入视频
    input_image_path = "watermark.png"  # 要叠加的图片
    output_video_path = "output.mp4"    # 输出视频
    
    # 调用函数进行处理
    overlay_image_on_video(
        input_video=input_video_path,
        input_image=input_image_path,
        output_video=output_video_path,
        opacity=0.5,  # 50%透明度
        position="center"  # 居中显示
    )
