import subprocess
import os
import glob

def overlay_image_on_video(
    input_video,
    input_image,
    output_video,
    opacity=0.4,
    ffmpeg_path=r"D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe"
):
    """
    将图片以40%透明度完全覆盖到视频上（1080*1920分辨率）

    参数:
    input_video: 输入视频文件路径
    input_image: 输入图片文件路径
    output_video: 输出视频文件路径
    opacity: 透明度，0-1之间，默认0.4（40%）
    ffmpeg_path: FFmpeg可执行文件路径
    """
    # 验证FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"FFmpeg未找到，请检查路径: {ffmpeg_path}")
    
    # 验证输入文件是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频不存在: {input_video}")
    
    if not os.path.exists(input_image):
        raise FileNotFoundError(f"输入图片不存在: {input_image}")
    
    # 构建FFmpeg命令 - 图片完全覆盖视频（1080*1920分辨率）
    cmd = [
        ffmpeg_path,
        "-i", input_video,
        "-i", input_image,
        "-filter_complex",
        f"[1:v]scale=1080:1920,format=rgba,colorchannelmixer=aa={opacity}[watermark];[0:v][watermark]overlay=0:0:shortest=0",
        "-c:a", "copy",  # 保持音频不变
        "-c:v", "libx264",  # 视频编码器
        "-preset", "medium",  # 编码预设
        "-crf", "23",  # 质量控制
        "-y",  # 覆盖现有文件
        output_video
    ]
    
    try:
        # 执行命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 检查是否有错误
        if result.returncode != 0:
            print(f"FFmpeg错误输出: {result.stderr}")
            raise RuntimeError(f"FFmpeg处理失败，返回代码: {result.returncode}")
            
        print(f"处理完成，输出文件: {output_video}")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def find_video_and_image_files():
    """
    在当前目录查找视频和图片文件
    """
    # 支持的视频格式
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv', '*.webm']
    # 支持的图片格式
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff', '*.webp']

    # 查找视频文件
    video_files = []
    for ext in video_extensions:
        video_files.extend(glob.glob(ext))

    # 查找图片文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))

    return video_files, image_files

if __name__ == "__main__":
    print("=== 视频水印融合工具 ===")
    print("正在查找当前目录下的视频和图片文件...")

    # 查找文件
    video_files, image_files = find_video_and_image_files()

    if not video_files:
        print("错误：当前目录下没有找到视频文件！")
        print("支持的视频格式：mp4, avi, mov, mkv, wmv, flv, webm")
        exit(1)

    if not image_files:
        print("错误：当前目录下没有找到图片文件！")
        print("支持的图片格式：png, jpg, jpeg, bmp, tiff, webp")
        exit(1)

    # 显示找到的文件
    print(f"\n找到 {len(video_files)} 个视频文件：")
    for i, video in enumerate(video_files, 1):
        print(f"  {i}. {video}")

    print(f"\n找到 {len(image_files)} 个图片文件：")
    for i, image in enumerate(image_files, 1):
        print(f"  {i}. {image}")

    # 选择文件（如果只有一个文件则自动选择）
    if len(video_files) == 1:
        selected_video = video_files[0]
        print(f"\n自动选择视频：{selected_video}")
    else:
        while True:
            try:
                choice = int(input(f"\n请选择视频文件 (1-{len(video_files)}): "))
                if 1 <= choice <= len(video_files):
                    selected_video = video_files[choice - 1]
                    break
                else:
                    print("请输入有效的数字！")
            except ValueError:
                print("请输入有效的数字！")

    if len(image_files) == 1:
        selected_image = image_files[0]
        print(f"自动选择图片：{selected_image}")
    else:
        while True:
            try:
                choice = int(input(f"\n请选择图片文件 (1-{len(image_files)}): "))
                if 1 <= choice <= len(image_files):
                    selected_image = image_files[choice - 1]
                    break
                else:
                    print("请输入有效的数字！")
            except ValueError:
                print("请输入有效的数字！")

    # 生成输出文件名
    video_name, video_ext = os.path.splitext(selected_video)
    output_video_path = f"{video_name}_水印融合{video_ext}"

    print(f"\n开始处理...")
    print(f"输入视频：{selected_video}")
    print(f"输入图片：{selected_image}")
    print(f"输出视频：{output_video_path}")
    print(f"图片透明度：40%")
    print(f"分辨率：1080×1920")

    # 调用函数进行处理
    success = overlay_image_on_video(
        input_video=selected_video,
        input_image=selected_image,
        output_video=output_video_path,
        opacity=0.4  # 40%透明度
    )

    if success:
        print(f"\n✅ 处理完成！输出文件：{output_video_path}")
    else:
        print(f"\n❌ 处理失败，请检查错误信息。")
