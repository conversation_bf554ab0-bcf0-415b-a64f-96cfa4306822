import subprocess
import os
import glob

def overlay_image_on_video(
    input_video,
    input_image,
    output_video,
    opacity=0.4,
    ffmpeg_path=r"D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe"
):
    """
    将图片以40%透明度完全覆盖到视频上（1080*1920分辨率）

    参数:
    input_video: 输入视频文件路径
    input_image: 输入图片文件路径
    output_video: 输出视频文件路径
    opacity: 透明度，0-1之间，默认0.4（40%）
    ffmpeg_path: FFmpeg可执行文件路径
    """
    # 验证FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"FFmpeg未找到，请检查路径: {ffmpeg_path}")
    
    # 验证输入文件是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频不存在: {input_video}")
    
    if not os.path.exists(input_image):
        raise FileNotFoundError(f"输入图片不存在: {input_image}")
    
    # 构建FFmpeg命令 - 图片完全覆盖视频（1080*1920分辨率）
    cmd = [
        ffmpeg_path,
        "-i", input_video,
        "-i", input_image,
        "-filter_complex",
        f"[1:v]scale=1080:1920,format=rgba,colorchannelmixer=aa={opacity}[watermark];[0:v][watermark]overlay=0:0:shortest=0",
        "-c:a", "copy",  # 保持音频不变
        "-c:v", "libx264",  # 视频编码器
        "-preset", "medium",  # 编码预设
        "-b:v", "12000k",  # 视频比特率设置为12000 kbps
        "-maxrate", "15000k",  # 最大比特率
        "-bufsize", "24000k",  # 缓冲区大小
        "-y",  # 覆盖现有文件
        output_video
    ]
    
    try:
        # 执行命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 检查是否有错误
        if result.returncode != 0:
            print(f"FFmpeg错误输出: {result.stderr}")
            raise RuntimeError(f"FFmpeg处理失败，返回代码: {result.returncode}")
            
        print(f"处理完成，输出文件: {output_video}")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def add_logo_watermark(
    input_video,
    logo_image,
    output_video,
    position="top-left",
    logo_width=220,
    margin=48,
    ffmpeg_path=r"D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe"
):
    """
    在视频上添加logo水印（100%不透明度）

    参数:
    input_video: 输入视频文件路径
    logo_image: logo图片文件路径
    output_video: 输出视频文件路径
    position: logo位置，可选"top-left", "top-right", "bottom-left", "bottom-right"
    logo_width: logo宽度（像素），默认220
    margin: 边距（像素），默认48
    ffmpeg_path: FFmpeg可执行文件路径
    """
    # 验证FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"FFmpeg未找到，请检查路径: {ffmpeg_path}")

    # 验证输入文件是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频不存在: {input_video}")

    if not os.path.exists(logo_image):
        raise FileNotFoundError(f"logo图片不存在: {logo_image}")

    # 根据位置确定logo坐标
    position_map = {
        "top-left": f"{margin}:{margin}",
        "top-right": f"W-w-{margin}:{margin}",
        "bottom-left": f"{margin}:H-h-{margin}",
        "bottom-right": f"W-w-{margin}:H-h-{margin}"
    }

    if position not in position_map:
        raise ValueError(f"不支持的位置: {position}，支持的位置: {list(position_map.keys())}")

    overlay_pos = position_map[position]

    # 构建FFmpeg命令 - 添加logo水印
    cmd = [
        ffmpeg_path,
        "-i", input_video,
        "-i", logo_image,
        "-filter_complex",
        f"[1:v]scale={logo_width}:-1[logo];[0:v][logo]overlay={overlay_pos}:shortest=0",
        "-c:a", "copy",  # 保持音频不变
        "-c:v", "libx264",  # 视频编码器
        "-preset", "medium",  # 编码预设
        "-b:v", "12000k",  # 视频比特率设置为12000 kbps
        "-maxrate", "15000k",  # 最大比特率
        "-bufsize", "24000k",  # 缓冲区大小
        "-y",  # 覆盖现有文件
        output_video
    ]

    try:
        # 执行命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 检查是否有错误
        if result.returncode != 0:
            print(f"FFmpeg错误输出: {result.stderr}")
            raise RuntimeError(f"FFmpeg处理失败，返回代码: {result.returncode}")

        print(f"Logo水印添加完成，输出文件: {output_video}")
        return True

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def add_moving_logo_watermark(
    input_video,
    logo_image,
    output_video,
    logo_width=220,
    opacity=0.06,
    max_speed_x=200,
    max_speed_y=200,
    ffmpeg_path=r"D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe"
):
    """
    在视频上添加动态移动的logo水印（6%透明度）

    参数:
    input_video: 输入视频文件路径
    logo_image: logo图片文件路径
    output_video: 输出视频文件路径
    logo_width: logo宽度（像素），默认220
    opacity: 透明度，默认0.06（6%）
    max_speed_x: X轴最大速度（像素/秒），默认200
    max_speed_y: Y轴最大速度（像素/秒），默认200
    ffmpeg_path: FFmpeg可执行文件路径
    """
    # 验证FFmpeg路径是否存在
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"FFmpeg未找到，请检查路径: {ffmpeg_path}")

    # 验证输入文件是否存在
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频不存在: {input_video}")

    if not os.path.exists(logo_image):
        raise FileNotFoundError(f"logo图片不存在: {logo_image}")

    # 创建动态移动的表达式
    # 使用正弦和余弦函数创建平滑的随机移动
    # t是时间变量，通过不同的频率和相位创建看似随机的移动
    x_expr = f"(W-w)/2 + {max_speed_x/4}*sin(0.3*t) + {max_speed_x/6}*cos(0.7*t + 1.5) + {max_speed_x/8}*sin(1.1*t + 3)"
    y_expr = f"(H-h)/2 + {max_speed_y/4}*cos(0.4*t) + {max_speed_y/6}*sin(0.6*t + 2) + {max_speed_y/8}*cos(0.9*t + 4.5)"

    # 确保logo不会移出屏幕边界
    x_expr = f"max(0, min(W-w, {x_expr}))"
    y_expr = f"max(0, min(H-h, {y_expr}))"

    # 构建FFmpeg命令 - 添加动态移动logo水印
    cmd = [
        ffmpeg_path,
        "-i", input_video,
        "-i", logo_image,
        "-filter_complex",
        f"[1:v]scale={logo_width}:-1,format=rgba,colorchannelmixer=aa={opacity}[logo];[0:v][logo]overlay=x='{x_expr}':y='{y_expr}':shortest=0",
        "-c:a", "copy",  # 保持音频不变
        "-c:v", "libx264",  # 视频编码器
        "-preset", "medium",  # 编码预设
        "-b:v", "12000k",  # 视频比特率设置为12000 kbps
        "-maxrate", "15000k",  # 最大比特率
        "-bufsize", "24000k",  # 缓冲区大小
        "-y",  # 覆盖现有文件
        output_video
    ]

    try:
        # 执行命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 检查是否有错误
        if result.returncode != 0:
            print(f"FFmpeg错误输出: {result.stderr}")
            raise RuntimeError(f"FFmpeg处理失败，返回代码: {result.returncode}")

        print(f"动态Logo水印添加完成，输出文件: {output_video}")
        return True

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def find_video_and_image_files():
    """
    在当前目录查找视频和图片文件
    """
    # 支持的视频格式
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv', '*.webm']
    # 支持的图片格式
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff', '*.webp']

    # 查找视频文件
    video_files = []
    for ext in video_extensions:
        video_files.extend(glob.glob(ext))

    # 查找图片文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))

    return video_files, image_files

if __name__ == "__main__":
    print("=== 视频水印融合工具 ===")
    print("1. 图片素材融合（完全覆盖，10%透明度）")
    print("2. Logo水印添加（100%不透明度，指定位置）")
    print("3. 动态Logo水印（6%透明度，全屏随机平滑移动）")

    # 选择功能
    while True:
        try:
            mode = int(input("\n请选择功能 (1-3): "))
            if mode in [1, 2, 3]:
                break
            else:
                print("请输入1、2或3！")
        except ValueError:
            print("请输入有效的数字！")

    print("\n正在查找当前目录下的视频和图片文件...")

    # 查找文件
    video_files, image_files = find_video_and_image_files()

    if not video_files:
        print("错误：当前目录下没有找到视频文件！")
        print("支持的视频格式：mp4, avi, mov, mkv, wmv, flv, webm")
        exit(1)

    if not image_files:
        print("错误：当前目录下没有找到图片文件！")
        print("支持的图片格式：png, jpg, jpeg, bmp, tiff, webp")
        exit(1)

    # 显示找到的文件
    print(f"\n找到 {len(video_files)} 个视频文件：")
    for i, video in enumerate(video_files, 1):
        print(f"  {i}. {video}")

    print(f"\n找到 {len(image_files)} 个图片文件：")
    for i, image in enumerate(image_files, 1):
        print(f"  {i}. {image}")

    # 选择文件（如果只有一个文件则自动选择）
    if len(video_files) == 1:
        selected_video = video_files[0]
        print(f"\n自动选择视频：{selected_video}")
    else:
        while True:
            try:
                choice = int(input(f"\n请选择视频文件 (1-{len(video_files)}): "))
                if 1 <= choice <= len(video_files):
                    selected_video = video_files[choice - 1]
                    break
                else:
                    print("请输入有效的数字！")
            except ValueError:
                print("请输入有效的数字！")

    if len(image_files) == 1:
        selected_image = image_files[0]
        print(f"自动选择图片：{selected_image}")
    else:
        while True:
            try:
                choice = int(input(f"\n请选择图片文件 (1-{len(image_files)}): "))
                if 1 <= choice <= len(image_files):
                    selected_image = image_files[choice - 1]
                    break
                else:
                    print("请输入有效的数字！")
            except ValueError:
                print("请输入有效的数字！")

    # 根据模式处理
    if mode == 1:
        # 图片素材融合模式
        video_name, video_ext = os.path.splitext(selected_video)
        output_video_path = f"{video_name}_素材融合{video_ext}"

        print(f"\n开始处理（图片素材融合）...")
        print(f"输入视频：{selected_video}")
        print(f"输入图片：{selected_image}")
        print(f"输出视频：{output_video_path}")
        print(f"图片透明度：10%")
        print(f"分辨率：1080×1920")
        print(f"视频比特率：12000 kbps")

        # 调用函数进行处理
        success = overlay_image_on_video(
            input_video=selected_video,
            input_image=selected_image,
            output_video=output_video_path,
            opacity=0.1  # 图片素材用0.1的参数
        )

    elif mode == 2:
        # Logo水印模式
        print("\nLogo位置选择：")
        print("1. 左上角")
        print("2. 右上角")
        print("3. 左下角")
        print("4. 右下角")

        position_map = {
            1: "top-left",
            2: "top-right",
            3: "bottom-left",
            4: "bottom-right"
        }

        while True:
            try:
                pos_choice = int(input("\n请选择Logo位置 (1-4): "))
                if pos_choice in position_map:
                    selected_position = position_map[pos_choice]
                    break
                else:
                    print("请输入1-4之间的数字！")
            except ValueError:
                print("请输入有效的数字！")

        video_name, video_ext = os.path.splitext(selected_video)
        output_video_path = f"{video_name}_Logo水印{video_ext}"

        print(f"\n开始处理（Logo水印）...")
        print(f"输入视频：{selected_video}")
        print(f"Logo图片：{selected_image}")
        print(f"输出视频：{output_video_path}")
        print(f"Logo位置：{['左上角', '右上角', '左下角', '右下角'][pos_choice-1]}")
        print(f"Logo宽度：220像素")
        print(f"边距：48像素")
        print(f"Logo透明度：100%（不透明）")
        print(f"视频比特率：12000 kbps")

        # 调用函数进行处理
        success = add_logo_watermark(
            input_video=selected_video,
            logo_image=selected_image,
            output_video=output_video_path,
            position=selected_position
        )

    else:
        # 动态Logo水印模式
        video_name, video_ext = os.path.splitext(selected_video)
        output_video_path = f"{video_name}_动态Logo水印{video_ext}"

        print(f"\n开始处理（动态Logo水印）...")
        print(f"输入视频：{selected_video}")
        print(f"Logo图片：{selected_image}")
        print(f"输出视频：{output_video_path}")
        print(f"Logo宽度：220像素")
        print(f"Logo透明度：6%")
        print(f"移动速度：X轴最大200px/s，Y轴最大200px/s")
        print(f"移动模式：全屏随机平滑移动")
        print(f"视频比特率：12000 kbps")

        # 调用函数进行处理
        success = add_moving_logo_watermark(
            input_video=selected_video,
            logo_image=selected_image,
            output_video=output_video_path
        )

    if success:
        print(f"\n✅ 处理完成！输出文件：{output_video_path}")
    else:
        print(f"\n❌ 处理失败，请检查错误信息。")
